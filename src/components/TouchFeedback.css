.touch-feedback {
  position: relative;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none; /* 防止iOS长按菜单 */
  -webkit-user-drag: none; /* 防止拖拽图片/元素 */
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  touch-action: manipulation; /* 防止双击缩放，允许拖拽 */
  transition: all 0.2s ease;
}

.touch-feedback--subtle {
  /* Minimal visual feedback */
}

.touch-feedback--prominent {
  /* More visible feedback */
  border-radius: 8px;
}

.touch-feedback--button {
  /* Button-like feedback */
  border-radius: 6px;
  transition: all 0.15s ease;
}

/* Touch states */
.touch-feedback:active:not(.touch-feedback--disabled) {
  transform: scale(0.98);
}

.touch-feedback--button:active:not(.touch-feedback--disabled) {
  transform: scale(0.96);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Hover states for non-touch devices */
@media (hover: hover) and (pointer: fine) {
  .touch-feedback--button:hover:not(.touch-feedback--disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .touch-feedback--prominent:hover:not(.touch-feedback--disabled) {
    background-color: rgba(79, 70, 229, 0.05);
  }
}

/* Disabled state */
.touch-feedback--disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

/* Focus states for accessibility */
.touch-feedback:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

.touch-feedback--button:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .touch-feedback {
    border: 1px solid currentColor;
  }
  
  .touch-feedback:focus-visible {
    outline: 3px solid #000;
  }
}

/* Reduce motion preferences */
@media (prefers-reduced-motion: reduce) {
  .touch-feedback {
    transition: none !important;
  }
  
  .touch-feedback:active {
    transform: none !important;
  }
  
  .touch-feedback--button:hover {
    transform: none !important;
  }
}

/* Loading state for async operations */
.touch-feedback--loading {
  position: relative;
  pointer-events: none;
}

.touch-feedback--loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: touch-feedback-spin 1s linear infinite;
}

@keyframes touch-feedback-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success state */
.touch-feedback--success {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
}

/* Error state */
.touch-feedback--error {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
}

/* Ripple container styles */
.touch-feedback .touch-ripple {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
  will-change: transform, opacity;
}

/* Enhanced feedback for touch devices */
@media (pointer: coarse) {
  .touch-feedback {
    min-height: 44px;
    min-width: 44px;
  }
  
  .touch-feedback--button {
    min-height: 48px;
    padding: 12px 16px;
  }
  
  /* Slightly more pronounced feedback on touch */
  .touch-feedback:active:not(.touch-feedback--disabled) {
    transform: scale(0.95);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .touch-feedback--prominent:hover:not(.touch-feedback--disabled) {
    background-color: rgba(99, 102, 241, 0.1);
  }
  
  .touch-feedback--success {
    background-color: rgba(16, 185, 129, 0.15);
  }
  
  .touch-feedback--error {
    background-color: rgba(239, 68, 68, 0.15);
  }
}