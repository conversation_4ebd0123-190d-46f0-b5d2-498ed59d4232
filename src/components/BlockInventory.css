.block-inventory {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-left: 1rem;
  min-width: 300px;
  max-height: 600px;
  overflow-y: auto;
}

.inventory-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  text-align: center;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.inventory-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  position: relative; /* Ensure proper positioning for absolute children */
  overflow: visible; /* Ensure blocks are not clipped */
}

.inventory-wrapper {
  /* Base styles for the scrollable wrapper */
  position: relative;
}

/* Desktop specific styling */
@media (min-width: 769px) {
  .block-inventory {
    min-width: 360px; /* Reduced width to fit content without horizontal scroll */
    max-width: 360px; /* Fixed max width */
    padding: 1.5rem; /* Increased padding for better spacing */
  }

  .inventory-wrapper {
    /* Scrollable container for desktop */
    overflow-x: hidden !important; /* No horizontal scroll */
    overflow-y: auto !important; /* Only vertical scroll */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .inventory-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .inventory-wrapper::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  .inventory-wrapper::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }

  .inventory-wrapper::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .inventory-container {
    overflow: visible; /* Allow proper block display within scrollable area */
    margin: 0; /* Remove auto centering for scrollable content */
  }

  .inventory-title {
    font-size: 1.3rem; /* Slightly larger title for desktop */
    margin-bottom: 1.5rem; /* More spacing below title */
  }
}

.inventory-container canvas {
  border-radius: 8px;
  cursor: grab;
}

.inventory-container canvas:active {
  cursor: grabbing;
}

.selected-block-info {
  background: rgba(79, 70, 229, 0.1);
  border: 2px solid #4f46e5;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 1rem;
}

.selected-block-info p {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
  color: #4f46e5;
}

.selected-block-info p.instruction {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 400;
  font-style: italic;
}

.block-controls {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #374151;
}

.btn-success {
  background: #059669;
  color: white;
}

.btn-success:hover {
  background: #047857;
}

.btn-warning {
  background: #d97706;
  color: white;
}

.btn-warning:hover {
  background: #b45309;
}

/* Touch-friendly interactions */
@media (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .inventory-container {
    /* Increase touch area for scrolling */
    padding: 4px;
  }
}

/* Container queries for inventory */
@container (max-width: 400px) {
  .inventory-title {
    font-size: 1rem;
  }
}

/* Mobile responsive design */
@media (max-width: 768px) {
  .block-inventory {
    margin: 0 auto;
    min-width: auto;
    width: 100%;
    max-width: 100vw;
    max-height: 40vh; /* Limit to 40% of viewport height */
    padding: 0.25rem; /* Tighter padding */
    overflow: visible; /* Allow proper layout */
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.8); /* Lighter background for mobile */
    box-shadow: none; /* Remove shadow */
  }

  .inventory-container {
    /* Center the container properly */
    max-width: 100%;
    overflow: visible;
    display: flex;
    justify-content: center;
    align-items: center;
    /* Remove scaling that causes display issues */
    transform: none;
    margin: 0 auto;
    width: fit-content;
  }

  .inventory-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    padding: 0;
    text-align: center;
    width: 100%;
  }

  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    min-height: 40px;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .block-inventory {
    padding: 0.25rem;
    max-height: 35vh; /* Further limit height */
    max-width: 100vw;
  }

  .inventory-container {
    /* Remove scaling - use smaller sizing instead */
    transform: none;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
  }

  .inventory-title {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
  }

  .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.7rem;
    min-height: 36px;
  }
}

/* Landscape orientation */
@media (orientation: landscape) and (max-height: 600px) {
  .block-inventory {
    max-height: 250px;
    padding: 0.5rem;
  }

  .inventory-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .inventory-container {
    scroll-behavior: auto;
  }

  .btn:hover {
    transform: none;
  }
}

/* Focus management */
.btn:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .block-inventory {
    border: 2px solid #000;
  }

  .btn {
    border: 2px solid currentColor;
  }

  .inventory-container {
    border: 1px solid #000;
  }
}

/* Safe area insets */
@supports (padding: max(0px)) {
  @media (max-width: 768px) {
    .block-inventory {
      margin-bottom: max(1rem, env(safe-area-inset-bottom));
    }
  }
}