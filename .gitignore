# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Test coverage
coverage/
.nyc_output/
*.lcov

# Environment variables
.env
.env.*
!.env.example

# Cloudflare Workers
.wrangler/
.dev.vars
.deploy/
wrangler.toml.backup

# Cache directories
.vite/
.cache/
.parcel-cache/

# IDE/Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Python artifacts (from solver scripts)
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
test_index.bin
solution.txt
black.txt

# Temporary files
*.tmp
*.temp
.tmp/
