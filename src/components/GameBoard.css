:root {
  --board-border-width: 2px;
  --board-border-radius: 8px;
  --container-border-radius: 12px;
}

.game-board-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--container-border-radius);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.3);
  /* 防止iOS文本选择和长按菜单 */
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.game-board-container canvas {
  border-radius: var(--board-border-radius);
  cursor: pointer;
  display: block;
  /* 防止iOS文本选择和长按菜单 */
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  pointer-events: auto;
}

/* Ensure consistent positioning for all child elements */
.game-board-container > * {
  position: relative;
}

/* Touch-friendly interactions */
@media (pointer: coarse) {
  .game-board-container canvas {
    /* Improve touch accuracy */
    touch-action: manipulation;
  }
}

/* Container queries for game board */
@container (max-width: 500px) {
  .game-board-container {
    /* Remove scaling to allow larger board on mobile */
    transform: none;
    transform-origin: center;
  }
}

/* Mobile responsive design */
@media (max-width: 768px) {
  .game-board-container {
    /* Keep padding at 0 for consistent coordinate system */
    padding: 0;
    margin: 0 auto;
    max-width: 100vw;
    width: fit-content; /* Size to content */
    overflow: visible; /* Allow proper centering */
    display: flex;
    justify-content: center;
    align-items: center;
    /* Remove any background and styling for cleaner mobile look */
    background: transparent;
    box-shadow: none;
    border: none;
  }

  .game-board-container canvas {
    max-width: calc(100vw - 10px); /* Minimal margin from edges */
    max-height: 60vh; /* Allow more height for board */
    width: auto;
    height: auto;
    display: block;
    margin: 0 auto;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .game-board-container {
    /* Remove scaling to fix coordinate issues */
    transform: none;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .game-board-container canvas {
    max-width: calc(100vw - 15px); /* Reduced margin for larger board */
    max-height: 55vh; /* Increased height for larger board */
  }
}

/* Landscape orientation */
@media (orientation: landscape) and (max-height: 600px) {
  .game-board-container {
    transform: none; /* Remove scaling for larger board */
    transform-origin: center top;
  }

  .game-board-container canvas {
    max-height: 65vh; /* Increased height for landscape */
  }
}

/* Very small screens */
@media (max-width: 320px) {
  .game-board-container {
    transform: none; /* Remove scaling for larger board */
    transform-origin: center top;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .game-board-container {
    transform: none !important;
    transition: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .game-board-container {
    border: 3px solid #000;
  }
}

/* Ensure proper positioning on mobile */
@supports (padding: max(0px)) {
  @media (max-width: 768px) {
    .game-board-container {
      margin-left: max(0, env(safe-area-inset-left));
      margin-right: max(0, env(safe-area-inset-right));
    }
  }
}

/* 有效位置提示动画 */
@keyframes validPositionPulse {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

@keyframes validPositionGlow {
  0% {
    box-shadow: 0 0 5px rgba(79, 70, 229, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(79, 70, 229, 0.6);
  }
  100% {
    box-shadow: 0 0 5px rgba(79, 70, 229, 0.3);
  }
}

/* 移动端拖拽时的有效位置样式 */
.mobile-valid-position {
  animation: validPositionPulse 1.5s ease-in-out infinite;
}

.mobile-valid-position-glow {
  animation: validPositionGlow 2s ease-in-out infinite;
}

/* 移动端拖拽提示 */
.mobile-drag-hint {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: fadeInUp 0.3s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .mobile-valid-position,
  .mobile-valid-position-glow,
  .mobile-drag-hint {
    animation: none !important;
  }
}