.app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 1rem;
}

.app-header {
  text-align: center;
  margin-bottom: 2rem;
  color: white;
}

.app-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  width: 100%;
  max-width: 1400px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.game-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.challenge-info h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.challenge-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.difficulty-beginner {
  background: #dcfce7;
  color: #166534;
}

.difficulty-advanced {
  background: #fef3c7;
  color: #92400e;
}

.difficulty-master {
  background: #fee2e2;
  color: #991b1b;
}

.difficulty-grandmaster {
  background: #ede9fe;
  color: #5b21b6;
}

.stats {
  font-weight: 500;
  color: #4b5563;
}

.game-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.completion-message {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  margin-top: 1rem;
}

.completion-message h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.completion-message p {
  margin: 0;
  opacity: 0.9;
}

.game-area {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
  /* 防止iOS文本选择和长按菜单 */
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #374151;
}

.btn-success {
  background: #059669;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #047857;
}

.btn-warning {
  background: #d97706;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #b45309;
}

/* Desktop-specific styles for proper block layout */
@media (min-width: 1025px) {
  .game-area {
    flex-direction: row;
    align-items: flex-start;
    justify-content: center;
    gap: 3rem; /* Increased gap for better spacing */
    flex-wrap: nowrap; /* Prevent wrapping on desktop */
    min-height: 500px; /* Ensure minimum height for proper display */
  }

  .block-inventory {
    flex-shrink: 0; /* Prevent inventory from shrinking */
    min-width: 500px; /* Ensure minimum width */
  }

  .game-container {
    max-width: 1600px; /* Increased max width for desktop */
    padding: 2.5rem; /* Increased padding */
  }
}

@media (max-width: 1024px) {
  .game-area {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* Ensure content starts from top */
  }
}

/* Drag and drop styles */
.drag-overlay {
  pointer-events: none !important;
  z-index: 9999 !important;
}

.dragging {
  opacity: 0.3 !important;
  transition: none !important;
}

.valid-drop-zone {
  background-color: rgba(14, 165, 233, 0.1) !important;
  border: 2px dashed #0ea5e9 !important;
}

.invalid-drop-zone {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border: 2px dashed #ef4444 !important;
}

/* Touch-friendly interactions */
@media (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .interactive-element,
  button,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Increase tap targets for mobile - but keep compact on small screens */
  .game-controls button {
    min-height: 44px;
    padding: 0.5rem 0.75rem;
  }

  /* Override for very small screens to be more compact */
  @media (max-width: 768px) {
    .game-controls button {
      min-height: 32px !important;
      padding: 0.25rem 0.5rem !important;
    }
  }
}

/* Container queries for adaptive layouts */
@container (max-width: 700px) {
  .game-area {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* Ensure content starts from top */
    gap: 1.5rem;
  }
}

@container (max-width: 600px) {
  .game-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .app {
    padding: 0; /* Remove all padding for tight layout */
    /* Account for mobile controls */
    padding-bottom: 120px; /* Reduced from 140px */
  }
  /* Hide header on mobile to save space and remove white space */
  .app-header {
    display: none;
  }
  
  /* Remove top spacing from konva canvas */
  .konvajs-content {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  
  canvas {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  .app-header h1 {
    font-size: 2rem;
  }
  /* Ensure main area takes full height and starts from top */
  .app-main {
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
  }

  .game-container {
    padding: 0.25rem 0.5rem 0 0.5rem; /* Reduced top padding further */
    border-radius: 0; /* Remove border radius for full width */
    margin: 0; /* Remove all margins */
    width: 100vw;
    max-width: 100vw;
    box-shadow: none; /* Remove shadow for cleaner mobile look */
    background: transparent; /* Remove background for cleaner look */
    gap: 0.25rem; /* Minimal gap between game-info and game-area */
  }

  .game-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .challenge-info h2 {
    font-size: 1.25rem; /* Smaller title */
    margin-bottom: 0.125rem; /* Minimal title spacing */
    line-height: 1.2; /* Tighter line height */
  }

  /* Make game-info more compact on mobile */
  .game-info {
    gap: 0.25rem !important; /* Tighter gap */
    padding-bottom: 0.125rem !important; /* Minimal bottom padding */
    margin-bottom: 0 !important; /* Remove bottom margin */
    border-bottom: none !important; /* Remove border for cleaner look */
  }

  .challenge-meta {
    gap: 0.5rem; /* Reduced gap */
    margin-bottom: 0.25rem; /* Add small bottom margin */
  }

  .game-controls {
    flex-wrap: wrap;
    gap: 0.125rem; /* Even tighter button spacing */
    justify-content: center;
    width: 100%;
    margin-bottom: 0.25rem; /* Minimal bottom margin */
  }

  .btn {
    padding: 0.25rem 0.4rem; /* Much smaller buttons */
    font-size: 0.65rem; /* Even smaller text */
    min-height: 30px; /* Much smaller minimum height */
    flex: 0 0 auto; /* Don't grow, use natural width */
    max-width: 70px; /* Much smaller max width */
    white-space: nowrap; /* Prevent text wrapping */
    line-height: 1.2; /* Tighter line height */
  }

  /* Use shorter text on mobile for better fit */
  .btn:contains("How to Play") {
    font-size: 0.6rem;
  }

  /* Hide title and description on mobile to save space */
  .app-header {
    display: none;
  }

  /* Optimize game area for mobile */
  .game-area {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* Start from top instead of center */
    gap: 0.375rem; /* Minimal gap */
    width: 100%;
    min-height: auto; /* Remove fixed height */
    overflow: visible; /* Allow natural content flow */
    padding: 0; /* Remove all padding */
    margin: 0; /* Remove all margin */
  }

  /* Mobile-specific adjustments */
  .completion-message {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .stats {
    font-size: 0.75rem; /* Smaller stats text */
  }

  .difficulty {
    font-size: 0.7rem; /* Smaller difficulty badge */
    padding: 0.2rem 0.6rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .app {
    padding: 0; /* Remove padding for maximum space */
    padding-bottom: 100px; /* Reduced bottom padding */
  }

  /* Hide header on small mobile devices too */
  .app-header {
    display: none;
  }

  .game-area {
    min-height: auto; /* Remove fixed height */
    justify-content: flex-start; /* Start from top */
  }

  .game-container {
    padding: 0.25rem; /* Minimal padding */
    border-radius: 0; /* Remove border radius for full width */
    background: transparent; /* Remove background */
  }

  .challenge-info h2 {
    font-size: 1.25rem;
  }

  .game-controls {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.2rem 0.35rem; /* Ultra compact buttons for small screens */
    font-size: 0.6rem; /* Very small text */
    min-height: 28px; /* Very compact height */
    flex: 0 0 auto; /* Don't grow, use natural width */
    max-width: 65px; /* Very small max width */
    white-space: nowrap; /* Prevent text wrapping */
    line-height: 1.1; /* Very tight line height */
  }

  .difficulty {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }

  .stats {
    font-size: 0.75rem;
  }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 600px) {
  .app {
    padding: 0.25rem;
    padding-bottom: 100px;
  }

  .app-header {
    margin-bottom: 1rem;
  }

  .app-header h1 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  .app-header p {
    font-size: 0.9rem;
  }

  .game-container {
    padding: 0.75rem;
  }

  .game-info {
    gap: 0.75rem;
  }

  .challenge-info h2 {
    font-size: 1.25rem;
  }

  .game-controls {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.25rem 0.5rem; /* Compact buttons for landscape */
    font-size: 0.7rem; /* Smaller text */
    min-height: 32px; /* Compact height */
    flex: 0 0 auto; /* Don't grow */
    max-width: 70px; /* Small max width */
    white-space: nowrap; /* Prevent text wrapping */
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .game-container {
    border-width: 0.5px;
  }

  .btn {
    border-width: 0.5px;
  }
}

/* Accessibility and motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .btn:hover:not(:disabled) {
    transform: none;
  }

  .btn:active:not(:disabled) {
    transform: none;
  }
}

/* Focus management for accessibility */
@media (prefers-reduced-motion: no-preference) {
  .btn:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
    transition: outline-offset 0.2s ease;
  }

  button:focus-visible,
  [role="button"]:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }

  .game-container {
    border: 2px solid #000;
  }

  .difficulty {
    border: 1px solid currentColor;
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .app {
    padding-left: max(0.5rem, env(safe-area-inset-left));
    padding-right: max(0.5rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  @media (max-width: 768px) {
    .app {
      padding-bottom: max(120px, calc(120px + env(safe-area-inset-bottom)));
    }
  }

  @media (max-width: 480px) {
    .app {
      padding-bottom: max(100px, calc(100px + env(safe-area-inset-bottom)));
    }
  }
}

/* 返回消息动画 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* SolverChallengeInfo 响应式显示控制 */
.solver-info-desktop {
  display: block;
}

.solver-info-mobile {
  display: none;
}

/* 移动端：隐藏桌面版本，显示移动版本 */
@media (max-width: 768px) {
  .solver-info-desktop {
    display: none;
  }

  .solver-info-mobile {
    display: block;
    margin-top: 1rem;
  }
}