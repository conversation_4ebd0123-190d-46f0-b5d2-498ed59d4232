{"metadata": {"total_solutions": 10, "unique_black_combinations": 4, "deduplication_method": "所有黑色积木被视为等效，只根据它们占用的格子位置进行去重", "start_time": "2025-07-20 20:52:24", "end_time": "2025-07-20 20:52:24", "elapsed_time_seconds": 0.003192424774169922, "board_size": 8}, "piece_definitions": {"K": {"name": "黑色1×3", "size": {"width": 1, "height": 3}}, "k": {"name": "黑色1×2", "size": {"width": 1, "height": 2}}, "x": {"name": "黑色1×1", "size": {"width": 1, "height": 1}}}, "black_piece_combinations": [{"combination_id": 1, "solution_id": 1, "black_pieces": {"K": {"name": "黑色1×3", "color": "K", "position": {"top_left": {"row": 0, "col": 7}, "bottom_right": {"row": 2, "col": 7}}, "size": {"width": 1, "height": 3}, "cells": [{"row": 0, "col": 7}, {"row": 1, "col": 7}, {"row": 2, "col": 7}]}, "x": {"name": "黑色1×1", "color": "x", "position": {"top_left": {"row": 3, "col": 7}, "bottom_right": {"row": 3, "col": 7}}, "size": {"width": 1, "height": 1}, "cells": [{"row": 3, "col": 7}]}, "k": {"name": "黑色1×2", "color": "k", "position": {"top_left": {"row": 5, "col": 5}, "bottom_right": {"row": 5, "col": 6}}, "size": {"width": 2, "height": 1}, "cells": [{"row": 5, "col": 5}, {"row": 5, "col": 6}]}}, "timestamp": "2025-07-20 20:52:24"}, {"combination_id": 2, "solution_id": 2, "black_pieces": {"K": {"name": "黑色1×3", "color": "K", "position": {"top_left": {"row": 0, "col": 7}, "bottom_right": {"row": 2, "col": 7}}, "size": {"width": 1, "height": 3}, "cells": [{"row": 0, "col": 7}, {"row": 1, "col": 7}, {"row": 2, "col": 7}]}, "x": {"name": "黑色1×1", "color": "x", "position": {"top_left": {"row": 3, "col": 7}, "bottom_right": {"row": 3, "col": 7}}, "size": {"width": 1, "height": 1}, "cells": [{"row": 3, "col": 7}]}, "k": {"name": "黑色1×2", "color": "k", "position": {"top_left": {"row": 2, "col": 5}, "bottom_right": {"row": 2, "col": 6}}, "size": {"width": 2, "height": 1}, "cells": [{"row": 2, "col": 5}, {"row": 2, "col": 6}]}}, "timestamp": "2025-07-20 20:52:24"}, {"combination_id": 3, "solution_id": 6, "black_pieces": {"K": {"name": "黑色1×3", "color": "K", "position": {"top_left": {"row": 2, "col": 5}, "bottom_right": {"row": 4, "col": 5}}, "size": {"width": 1, "height": 3}, "cells": [{"row": 2, "col": 5}, {"row": 3, "col": 5}, {"row": 4, "col": 5}]}, "k": {"name": "黑色1×2", "color": "k", "position": {"top_left": {"row": 4, "col": 6}, "bottom_right": {"row": 5, "col": 6}}, "size": {"width": 1, "height": 2}, "cells": [{"row": 4, "col": 6}, {"row": 5, "col": 6}]}, "x": {"name": "黑色1×1", "color": "x", "position": {"top_left": {"row": 5, "col": 5}, "bottom_right": {"row": 5, "col": 5}}, "size": {"width": 1, "height": 1}, "cells": [{"row": 5, "col": 5}]}}, "timestamp": "2025-07-20 20:52:24"}, {"combination_id": 4, "solution_id": 9, "black_pieces": {"K": {"name": "黑色1×3", "color": "K", "position": {"top_left": {"row": 0, "col": 7}, "bottom_right": {"row": 2, "col": 7}}, "size": {"width": 1, "height": 3}, "cells": [{"row": 0, "col": 7}, {"row": 1, "col": 7}, {"row": 2, "col": 7}]}, "x": {"name": "黑色1×1", "color": "x", "position": {"top_left": {"row": 3, "col": 7}, "bottom_right": {"row": 3, "col": 7}}, "size": {"width": 1, "height": 1}, "cells": [{"row": 3, "col": 7}]}, "k": {"name": "黑色1×2", "color": "k", "position": {"top_left": {"row": 0, "col": 6}, "bottom_right": {"row": 1, "col": 6}}, "size": {"width": 1, "height": 2}, "cells": [{"row": 0, "col": 6}, {"row": 1, "col": 6}]}}, "timestamp": "2025-07-20 20:52:24"}]}