.swipe-indicator {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  border-radius: 16px;
  padding: 20px 24px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  max-width: 280px;
  text-align: center;
  animation: swipe-indicator-fade-in 0.3s ease-out;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.swipe-indicator--top {
  top: 20px;
}

.swipe-indicator--center {
  top: 50%;
  transform: translate(-50%, -50%);
}

.swipe-indicator--bottom {
  bottom: 100px; /* Leave space for mobile controls */
}

.swipe-indicator__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  position: relative;
}

.swipe-indicator__animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 40px;
}

.swipe-indicator--vertical .swipe-indicator__animation {
  flex-direction: column;
  width: 40px;
  height: 60px;
}

.swipe-arrow {
  opacity: 0.7;
  animation: swipe-arrow-pulse 2s ease-in-out infinite;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swipe-arrow--left {
  animation-delay: 0s;
}

.swipe-arrow--right {
  animation-delay: 1s;
}

.swipe-arrow--up {
  animation-delay: 0s;
}

.swipe-arrow--down {
  animation-delay: 1s;
}

.swipe-gesture-line {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 20%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0.3) 80%, 
    transparent 100%
  );
  border-radius: 1px;
  position: relative;
  overflow: hidden;
}

.swipe-gesture-line--vertical {
  width: 2px;
  height: 60px;
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 20%, 
    rgba(255, 255, 255, 0.8) 50%, 
    rgba(255, 255, 255, 0.3) 80%, 
    transparent 100%
  );
}

.swipe-gesture-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: -20px;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: swipe-shimmer 2s ease-in-out infinite;
}

.swipe-gesture-line--vertical::after {
  top: -20px;
  left: 0;
  width: 100%;
  height: 20px;
  background: linear-gradient(180deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}

.swipe-both-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.swipe-circle {
  animation: swipe-rotate 3s linear infinite;
}

.swipe-indicator__text {
  font-size: 13px;
  opacity: 0.9;
  line-height: 1.4;
}

.swipe-indicator__close {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.swipe-indicator__close:hover {
  background: rgba(255, 255, 255, 0.3);
  opacity: 1;
  transform: scale(1.1);
}

.swipe-indicator__close:active {
  transform: scale(0.95);
}

/* Animations */
@keyframes swipe-indicator-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes swipe-arrow-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes swipe-shimmer {
  0% {
    left: -20px;
  }
  100% {
    left: 100%;
  }
}

@keyframes swipe-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .swipe-indicator {
    max-width: 260px;
    padding: 16px 20px;
    font-size: 13px;
  }

  .swipe-indicator--bottom {
    bottom: 80px; /* Adjust for mobile controls */
  }

  .swipe-indicator__animation {
    height: 36px;
  }

  .swipe-indicator--vertical .swipe-indicator__animation {
    height: 50px;
  }

  .swipe-gesture-line {
    width: 50px;
  }

  .swipe-gesture-line--vertical {
    height: 50px;
  }
}

/* Landscape mode adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .swipe-indicator--bottom {
    bottom: 60px;
  }

  .swipe-indicator {
    padding: 12px 16px;
    font-size: 12px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .swipe-indicator {
    background: #000;
    border: 2px solid #fff;
  }

  .swipe-arrow {
    opacity: 1;
  }

  .swipe-gesture-line {
    background: #fff;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .swipe-indicator {
    animation: none;
  }

  .swipe-arrow {
    animation: none;
    opacity: 0.8;
  }

  .swipe-gesture-line::after {
    animation: none;
  }

  .swipe-circle {
    animation: none;
  }

  .swipe-indicator__close {
    transition: none;
  }
}