# 黑色方块布局难度评分系统

## 概述

本系统为8×8棋盘上的黑色方块布局提供多维度的难度评分，帮助自动生成不同难度等级的挑战。评分系统考虑了6个主要因子，每个因子都会影响玩家完成拼图的难度。

## 评分因子

### 1. 分散程度 (Spread Score)
- **计算方法**: `(max_x - min_x) + (max_y - min_y) * 10`
- **最大分数**: 100分
- **影响**: 黑色方块分布越分散，玩家需要考虑的布局约束越多
- **示例**: 
  - 所有黑色方块在同一行/列: 0分
  - 分布在对角线两端: 140分 (被限制为100分)

### 2. 碎片化程度 (Fragmentation Score)
- **计算方法**: `(独立区域数 - 1) * 30`
- **影响**: 独立的黑色方块区域越多，布局复杂度越高
- **示例**:
  - 所有黑色方块连通: 0分
  - 2个独立区域: 30分
  - 3个独立区域: 60分

### 3. 边缘接近度 (Edge Proximity Score)
- **计算方法**: `max(0, (3 - 平均边缘距离) * 20)`
- **影响**: 黑色方块越靠近边缘，可用的放置选择越少
- **示例**:
  - 平均距离边缘3格以上: 0分
  - 平均距离边缘0格(贴边): 60分

### 4. 连通性评分 (Connectivity Score)
- **计算方法**: `(1 - 相邻率) * 40`
- **影响**: 黑色方块间连接度越低，形成的约束越复杂
- **示例**:
  - 完全连通的直线: 0分
  - 完全分离的点: 40分

### 5. 对称性评分 (Symmetry Score)
- **计算方法**: 对称布局 -20分，非对称布局 0分
- **影响**: 对称布局通常更容易识别模式，降低难度
- **检查**: 水平对称或垂直对称

### 6. 角落占用评分 (Corner Occupation Score)
- **计算方法**: `占用角落数 * 15`
- **影响**: 角落位置限制了大型积木的放置选择
- **示例**:
  - 无角落占用: 0分
  - 占用4个角落: 60分

## 难度等级划分

| 等级 | 分数范围 | 中文名称 | 特征 |
|------|----------|----------|------|
| beginner | 0-49 | 入门 | 黑色方块相对集中，约束较少 |
| advanced | 50-99 | 进阶 | 中等分散度，有一定约束 |
| master | 100-149 | 大师 | 高分散度或多重约束 |
| grandmaster | 150+ | 宗师 | 极高复杂度，多重约束叠加 |

## 使用方法

### 在 Python 求解器中
```python
# 在 tiling_solver.py 中自动计算
difficulty_score = solver.calculate_difficulty_score(black_pieces_info)
print(f"难度评分: {difficulty_score['total_score']} ({difficulty_score['difficulty']})")
```

### 在 TypeScript/React 中
```typescript
import { getDifficultyScoreForCombination } from '../data/blackPositions';

const score = getDifficultyScoreForCombination(combinationId);
console.log(`难度: ${score.difficulty}, 总分: ${score.totalScore}`);
```

### 命令行分析工具
```bash
# 分析现有的黑色方块位置文件
python3 analyze_difficulty.py black_positions.json

# 输出分析结果到文件
python3 analyze_difficulty.py black_positions.json -o difficulty_analysis.txt
```

## 评分示例

### 示例1: 简单布局 (入门级)
```
黑色方块位置: (0,0), (0,1), (0,2), (1,0), (1,1), (2,0)
- 分散程度: 20分 (2格距离)
- 碎片化: 0分 (1个区域)
- 边缘接近: 60分 (贴边)
- 连通性: 0分 (高连通性)
- 对称性: 0分 (非对称)
- 角落占用: 15分 (1个角落)
总分: 95分 (进阶)
```

### 示例2: 复杂布局 (宗师级)
```
黑色方块位置: (0,7), (1,7), (2,7), (3,7), (5,5), (5,6)
- 分散程度: 70分 (7格距离)
- 碎片化: 30分 (2个区域)
- 边缘接近: 50分 (平均0.5格)
- 连通性: 13.33分 (中等连通性)
- 对称性: 0分 (非对称)
- 角落占用: 15分 (1个角落)
总分: 178.33分 (宗师)
```

## 调优建议

1. **平衡性**: 确保各难度等级的挑战数量相对均衡
2. **渐进性**: 同一等级内的挑战应该有适当的分数梯度
3. **验证**: 通过实际游戏测试验证评分与实际难度的相关性
4. **自定义**: 可以根据具体游戏需求调整各因子的权重

## 扩展性

系统设计为可扩展的，可以轻松添加新的评分因子：
- 特殊形状识别
- 玩家历史数据
- 时间限制影响
- 提示系统集成
