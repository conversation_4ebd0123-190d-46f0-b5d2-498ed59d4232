{"name": "blockcanvas", "version": "1.0.0", "description": "", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "deploy": "vite build && wrangler deploy", "deploy:info": "npm run deploy && echo 'Deployed to: https://blockcanvas.zhiyue.workers.dev' && echo 'Custom domain: https://blockcanvas.deploy.cv'", "domain:show": "echo 'Default URL: https://blockcanvas.cszhiyue.workers.dev' && echo 'Custom domain: https://blockcanvas.deploy.cv (configured in wrangler.jsonc)'", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "typecheck": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "keywords": ["mondrian", "blocks", "puzzle", "game"], "author": "", "license": "MIT", "type": "module", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@stagewise-plugins/react": "^0.6.1", "@stagewise/toolbar-react": "^0.6.1", "konva": "^9.3.22", "react": "^19.1.0", "react-dom": "^19.1.0", "react-konva": "^19.0.7", "zustand": "^5.0.6"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.9.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "jsdom": "^26.1.0", "typescript": "^5.8.3", "vite": "^7.0.5", "vitest": "^3.2.4", "wrangler": "^4.25.0"}}