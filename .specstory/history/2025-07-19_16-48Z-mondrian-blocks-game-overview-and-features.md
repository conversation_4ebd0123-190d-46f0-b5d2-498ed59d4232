<!-- Generated by SpecStory -->

# Mondrian Blocks game overview and features (2025-07-19 16:48Z)

_**User**_

Mondrian Blocks is an award-winning, single-player logic puzzle game that challenges players to fit a set of colorful blocks into an 8x8 grid, inspired by the abstract art of <PERSON><PERSON>dr<PERSON>. It's designed to enhance spatial reasoning, problem-solving, and cognitive flexibility, making it suitable for ages 8 and up.Amazon+1Puzzle Master, Inc.+1Walmart.com+1More 2 Science+1
🧩 Game Components
Each Mondrian Blocks set includes:
* 11 uniquely shaped blocks
* 88 challenge cards, divided into four difficulty levels: Beginner, Advanced, Master, and Grandmaster
* A compact travel case that doubles as the game board and storage
* A card compartment for easy organizationAre You Game UK+4More 2 Science+4The Therapy Store+4Amazon+5learningbydoing.fi+5Are You Game UK+5
The game is available in four editions—Red, Yellow, Blue, and White—each offering a distinct set of 88 puzzles, providing a fresh experience with every edition. The Mind Games+4learningbydoing.fi+4More 2 Science+4 快速开发一个网页版的上面游戏,技术要求如下:1 | 产品定位 & 关键功能
维度目标目标人群休闲益智玩家、幼儿园~成人逻辑训练者核心循环① 选关 → ② 拖放/点击放置积木 → ③ 实时冲突提示 → ④ 填满即通关必备功能关卡库加载、拖拽+触屏交互、自动校验、存档、音效/粒子反馈进阶功能⬥ 计时&排行榜 ⬥ 主题皮肤 ⬥ 关卡编辑器 ⬥ PWA 离线体验
2 | 技术选型
层次推荐方案说明 & 参考前端框架React 18 + Vite 5 + TypeScript快速热更、良好生态渲染react-konva（Canvas）在移动端比 DOM/Grid 更流畅，支持像素级拖拽 GitHub拖拽@dnd-kit/core轻量、可定制、支持键盘/触摸，比老的 react-beautiful-dnd 更活跃 dndkit.com状态管理Zustand无样板代码、可持久化、足够轻 DEV Community测试Vitest + React Testing Library + Playwright打包/部署Cloudflare Pages / Vercel；开启 PWA 插件，支持离线
🎮 How to Play
1. Select a Challenge Card: Open the card compartment, choose a challenge card, and place it in the designated holder.
2. Set Up the Board: Arrange the fixed starter blocks on the 8x8 grid as indicated on the challenge card.
3. Solve the Puzzle: Use the remaining blocks to fill the grid completely without overlapping. Each puzzle has only one correct solution.More 2 Science+3The Therapy Store+3learningbydoing.fi+3Google Play
The game can be played solo or competitively by racing against others to solve the same puzzle.

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Translating project requirements**

The user seems to be asking for a quick development of a web version of a game, specifically the Mondrian Blocks puzzle game. They’ve outlined a set of technical requirements like using React, Vite, TypeScript, and other libraries for features like drag-and-drop interactions, state management, and visual effects. It sounds like I need to start by creating a code skeleton that follows these technical choices and implements core functionalities, such as level loading and saving progress.</details></think>

---

<details>
            <summary>Listed current directory • **1** results</summary>
        
| Name |
|-------|
| 📁 `.specstory` |

</details>

---

<think><details><summary>Thought Process</summary>**Planning shape implementation**

I’m setting up a plan before diving into coding. First, I need some clarifications, like confirming the board size of 8x8 and the 11 unique shapes. I should define those shapes' coordinates, maybe approximating them as puzzle pieces like in Rushhour. Researching the Mondrian Blocks, which feature similar polyomino shapes, could help. It might be easier to implement a generic shape representation and load definitions from a level card for simplicity. Let’s get started with the tasks!</details></think>

---



---

已为「Mondrian Blocks」网页版开发创建了详细的任务清单（14 项），涵盖从项目初始化、核心玩法实现到 PWA 离线支持与测试。  
接下来会逐步将任务状态更新为进行中并开始编码，首先从 Vite+React+TS 脚手架搭建及依赖安装开始。如果对任务拆分或优先级有任何建议，请告诉我！

---

_**User**_

把方案先写到一个文档

---

_**Assistant**_



---

