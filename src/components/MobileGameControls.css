.mobile-game-controls {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

/* Floating Control Panel */
.floating-controls {
  position: absolute;
  bottom: 120px;
  right: 20px;
  pointer-events: auto;
  z-index: 1001;
}

.control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  animation: slideInUp 0.3s ease-out;
}

.control-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.selected-block-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.rotation-indicator {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-btn {
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  min-height: 48px;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.control-btn:hover {
  background: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.control-btn:active {
  transform: translateY(0);
}

.rotate-btn {
  background: #059669;
}

.rotate-btn:hover {
  background: #047857;
}

.clear-btn {
  background: #dc2626;
}

.clear-btn:hover {
  background: #b91c1c;
}

/* Mode Indicator */
.mode-indicator {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  pointer-events: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.mode-badge {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 24px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: slideInUp 0.3s ease-out;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.mode-badge:active {
  transform: translateY(0);
}

.mode-badge.mode-tap {
  color: #059669;
  border-color: rgba(5, 150, 105, 0.3);
}

.mode-badge.mode-drag {
  color: #4f46e5;
  border-color: rgba(79, 70, 229, 0.3);
}

.quick-instructions {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 12px;
  text-align: center;
  max-width: 200px;
  animation: slideInUp 0.3s ease-out 0.1s both;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .floating-controls {
    right: 16px;
    bottom: 100px;
  }

  .control-panel {
    padding: 12px;
    min-width: 180px;
  }

  .control-btn {
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
  }

  .mode-indicator {
    bottom: 16px;
    left: 16px;
    right: 16px;
  }

  .quick-instructions {
    font-size: 11px;
    padding: 6px 10px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .floating-controls {
    bottom: 80px;
    right: 16px;
  }

  .mode-indicator {
    bottom: 12px;
    left: 12px;
    right: 12px;
  }

  .control-panel {
    padding: 10px;
    min-width: 160px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .control-panel {
    border-width: 0.5px;
  }
  
  .mode-badge {
    border-width: 0.5px;
  }
}

/* Reduce motion preference */
@media (prefers-reduced-motion: reduce) {
  .control-panel,
  .mode-badge,
  .quick-instructions {
    animation: none;
  }
  
  .control-btn {
    transition: none;
  }
}